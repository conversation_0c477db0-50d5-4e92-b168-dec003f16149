{% extends "base.html" %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Email Templates</h1>
                <a href="{{ url_for('main.new_template') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>New Template
                </a>
            </div>
        </div>
    </div>

    {% if templates %}
        <div class="row g-4">
            {% for template in templates %}
                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                {{ template.name }}
                                {% if template.is_default %}
                                    <span class="badge bg-primary ms-2">Default</span>
                                {% endif %}
                            </h5>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-three-dots"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{{ url_for('main.edit_template', id=template.id) }}">
                                        <i class="bi bi-pencil me-2"></i>Edit
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form method="POST" action="{{ url_for('main.delete_template', id=template.id) }}" class="d-inline">
                                            <button type="submit" class="dropdown-item text-danger" 
                                                    onclick="return confirm('Are you sure you want to delete this template?')">
                                                <i class="bi bi-trash me-2"></i>Delete
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="card-body">
                            <h6 class="card-subtitle mb-2 text-muted">Subject:</h6>
                            <p class="card-text">{{ template.subject }}</p>
                            
                            <h6 class="card-subtitle mb-2 text-muted">Body Preview:</h6>
                            <div class="template-preview">
                                {{ template.body[:200] }}{% if template.body|length > 200 %}...{% endif %}
                            </div>
                            
                            <div class="mt-3">
                                <small class="text-muted">
                                    Created: {{ template.created_at.strftime('%B %d, %Y') }}
                                    {% if template.updated_at != template.created_at %}
                                        | Updated: {{ template.updated_at.strftime('%B %d, %Y') }}
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="bi bi-envelope me-1"></i>
                                    Used in {{ template.job_searches|length }} searches
                                </small>
                                <div>
                                    <a href="{{ url_for('main.edit_template', id=template.id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-pencil me-1"></i>Edit
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="bi bi-envelope display-4 text-muted mb-3"></i>
                        <h4 class="text-muted">No Email Templates</h4>
                        <p class="text-muted mb-4">Create your first email template to start sending professional job applications.</p>
                        <a href="{{ url_for('main.new_template') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>Create Your First Template
                        </a>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Template Variables Help -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-info-circle me-2"></i>Available Template Variables
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Use these variables in your templates to personalize emails:</p>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><code>{company_name}</code> - Company name</li>
                                <li><code>{position}</code> - Job position</li>
                                <li><code>{hr_name}</code> - HR contact name</li>
                                <li><code>{hr_email}</code> - HR contact email</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><code>{user_name}</code> - Your full name</li>
                                <li><code>{user_email}</code> - Your email address</li>
                                <li><code>{company_domain}</code> - Company website</li>
                                <li><code>{date}</code> - Current date</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
