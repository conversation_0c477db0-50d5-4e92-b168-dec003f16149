{% extends "base.html" %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">{{ job_search.company_name }}</h1>
                <div>
                    <a href="{{ url_for('main.job_searches') }}" class="btn btn-outline-secondary me-2">
                        <i class="bi bi-arrow-left me-1"></i>Back to Searches
                    </a>
                    {% if not job_search.email_sent %}
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#sendEmailModal">
                            <i class="bi bi-envelope me-1"></i>Send Email
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- Job Search Details -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-building me-2"></i>Company Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Company Name:</strong> {{ job_search.company_name }}</p>
                            {% if job_search.company_domain %}
                                <p><strong>Domain:</strong> 
                                    <a href="https://{{ job_search.company_domain }}" target="_blank" class="text-decoration-none">
                                        {{ job_search.company_domain }} <i class="bi bi-box-arrow-up-right"></i>
                                    </a>
                                </p>
                            {% endif %}
                            {% if job_search.position %}
                                <p><strong>Position:</strong> {{ job_search.position }}</p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <p><strong>Status:</strong> 
                                <span class="badge bg-{{ job_search.get_status_color() }}">{{ job_search.status.title() }}</span>
                            </p>
                            <p><strong>Created:</strong> {{ job_search.created_at.strftime('%B %d, %Y at %I:%M %p') }}</p>
                            {% if job_search.updated_at != job_search.created_at %}
                                <p><strong>Updated:</strong> {{ job_search.updated_at.strftime('%B %d, %Y at %I:%M %p') }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if job_search.notes %}
                        <hr>
                        <h6>Notes:</h6>
                        <p class="text-muted">{{ job_search.notes }}</p>
                    {% endif %}
                </div>
            </div>

            <!-- HR Contact Information -->
            {% if job_search.hr_name or job_search.hr_email %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-person-lines-fill me-2"></i>HR Contact
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                {% if job_search.hr_name %}
                                    <p><strong>Name:</strong> {{ job_search.hr_name }}</p>
                                {% endif %}
                                {% if job_search.hr_email %}
                                    <p><strong>Email:</strong> 
                                        <a href="mailto:{{ job_search.hr_email }}" class="text-decoration-none">
                                            {{ job_search.hr_email }}
                                        </a>
                                    </p>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                {% if job_search.hr_linkedin %}
                                    <p><strong>LinkedIn:</strong> 
                                        <a href="{{ job_search.hr_linkedin }}" target="_blank" class="text-decoration-none">
                                            View Profile <i class="bi bi-box-arrow-up-right"></i>
                                        </a>
                                    </p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Email History -->
            {% if job_search.email_sent %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-envelope-check me-2"></i>Email History
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle me-2"></i>
                            Email sent successfully on {{ job_search.email_sent_at.strftime('%B %d, %Y at %I:%M %p') }}
                        </div>
                        
                        {% if job_search.email_template_used %}
                            <p><strong>Template Used:</strong> {{ job_search.email_template_used.name }}</p>
                        {% endif %}
                        
                        {% if job_search.smtp_config_used %}
                            <p><strong>SMTP Config:</strong> {{ job_search.smtp_config_used.name }}</p>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Actions Sidebar -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if not job_search.email_sent %}
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#sendEmailModal">
                                <i class="bi bi-envelope me-2"></i>Send Email
                            </button>
                        {% else %}
                            <button class="btn btn-outline-primary" disabled>
                                <i class="bi bi-check-circle me-2"></i>Email Sent
                            </button>
                        {% endif %}
                        
                        <a href="{{ url_for('main.search') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-plus-circle me-2"></i>New Search
                        </a>
                        
                        <button class="btn btn-outline-danger" onclick="updateStatus('rejected')">
                            <i class="bi bi-x-circle me-2"></i>Mark as Rejected
                        </button>
                        
                        <button class="btn btn-outline-success" onclick="updateStatus('responded')">
                            <i class="bi bi-reply me-2"></i>Mark as Responded
                        </button>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">Quick Info</h6>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <p class="mb-1"><strong>Search ID:</strong> #{{ job_search.id }}</p>
                        <p class="mb-1"><strong>Days Since Created:</strong> {{ (now - job_search.created_at).days }}</p>
                        {% if job_search.email_sent %}
                            <p class="mb-0"><strong>Days Since Email:</strong> {{ (now - job_search.email_sent_at).days }}</p>
                        {% endif %}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Send Email Modal -->
{% if not job_search.email_sent and email_templates and smtp_configs %}
<div class="modal fade" id="sendEmailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Send Email to {{ job_search.company_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="send-email-form">
                <div class="modal-body">
                    <input type="hidden" name="job_search_id" value="{{ job_search.id }}">
                    <input type="hidden" name="company_name" value="{{ job_search.company_name }}">
                    <input type="hidden" name="company_domain" value="{{ job_search.company_domain or '' }}">
                    <input type="hidden" name="position" value="{{ job_search.position or '' }}">
                    <input type="hidden" name="hr_name" value="{{ job_search.hr_name or '' }}">
                    <input type="hidden" name="hr_email" value="{{ job_search.hr_email or '' }}">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="template_id" class="form-label">Email Template</label>
                                <select class="form-select" id="template_id" name="template_id" required>
                                    <option value="">Select template...</option>
                                    {% for template in email_templates %}
                                        <option value="{{ template.id }}" 
                                                data-subject="{{ template.subject }}" 
                                                data-body="{{ template.body }}"
                                                {{ 'selected' if template.is_default }}>
                                            {{ template.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp_config_id" class="form-label">SMTP Configuration</label>
                                <select class="form-select" id="smtp_config_id" name="smtp_config_id" required>
                                    <option value="">Select SMTP config...</option>
                                    {% for config in smtp_configs %}
                                        <option value="{{ config.id }}" {{ 'selected' if config.is_active }}>
                                            {{ config.name }}
                                            {% if config.is_verified %}
                                                <span class="text-success">✓</span>
                                            {% endif %}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div id="template-preview" class="mb-3">
                        <p class="text-muted">Select a template to preview</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="send-email-btn">
                        <i class="bi bi-envelope me-2"></i>Send Email
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}

<div id="alert-container"></div>
{% endblock %}

{% block scripts %}
<script>
function updateStatus(status) {
    if (confirm(`Are you sure you want to mark this job search as ${status}?`)) {
        fetch(`/api/job-search/{{ job_search.id }}/status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                JobSearchApp.showAlert('Failed to update status: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            JobSearchApp.showAlert('Error updating status: ' + error.message, 'danger');
        });
    }
}
</script>
{% endblock %}
