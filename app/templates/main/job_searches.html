{% extends "base.html" %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Job Searches</h1>
                <a href="{{ url_for('main.search') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>New Search
                </a>
            </div>
        </div>
    </div>

    {% if searches.items %}
        <!-- Filter and Sort Options -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" class="row g-3 align-items-end">
                            <div class="col-md-3">
                                <label for="status" class="form-label">Filter by Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Statuses</option>
                                    <option value="researching" {{ 'selected' if request.args.get('status') == 'researching' }}>Researching</option>
                                    <option value="contacted" {{ 'selected' if request.args.get('status') == 'contacted' }}>Contacted</option>
                                    <option value="responded" {{ 'selected' if request.args.get('status') == 'responded' }}>Responded</option>
                                    <option value="rejected" {{ 'selected' if request.args.get('status') == 'rejected' }}>Rejected</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="search" class="form-label">Search Companies</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="{{ request.args.get('search', '') }}" placeholder="Company name...">
                            </div>
                            <div class="col-md-3">
                                <label for="sort" class="form-label">Sort by</label>
                                <select class="form-select" id="sort" name="sort">
                                    <option value="created_desc" {{ 'selected' if request.args.get('sort') == 'created_desc' }}>Newest First</option>
                                    <option value="created_asc" {{ 'selected' if request.args.get('sort') == 'created_asc' }}>Oldest First</option>
                                    <option value="company_asc" {{ 'selected' if request.args.get('sort') == 'company_asc' }}>Company A-Z</option>
                                    <option value="company_desc" {{ 'selected' if request.args.get('sort') == 'company_desc' }}>Company Z-A</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="bi bi-funnel me-1"></i>Filter
                                </button>
                                <a href="{{ url_for('main.job_searches') }}" class="btn btn-outline-secondary ms-2">
                                    <i class="bi bi-x-circle me-1"></i>Clear
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Job Searches List -->
        <div class="row g-4">
            {% for search in searches.items %}
                <div class="col-12">
                    <div class="job-search-card card {{ search.status }}">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h5 class="card-title mb-2">
                                        <a href="{{ url_for('main.job_detail', id=search.id) }}" class="text-decoration-none">
                                            {{ search.company_name }}
                                        </a>
                                        <span class="badge bg-{{ search.get_status_color() }} ms-2">{{ search.status.title() }}</span>
                                    </h5>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            {% if search.position %}
                                                <p class="card-text mb-1"><strong>Position:</strong> {{ search.position }}</p>
                                            {% endif %}
                                            {% if search.company_domain %}
                                                <p class="card-text mb-1">
                                                    <strong>Domain:</strong> 
                                                    <a href="https://{{ search.company_domain }}" target="_blank" class="text-decoration-none">
                                                        {{ search.company_domain }} <i class="bi bi-box-arrow-up-right"></i>
                                                    </a>
                                                </p>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6">
                                            {% if search.hr_name %}
                                                <p class="card-text mb-1"><strong>HR Contact:</strong> {{ search.hr_name }}</p>
                                            {% endif %}
                                            {% if search.hr_email %}
                                                <p class="card-text mb-1">
                                                    <strong>Email:</strong> 
                                                    <a href="mailto:{{ search.hr_email }}" class="text-decoration-none">
                                                        {{ search.hr_email }}
                                                    </a>
                                                </p>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <small class="text-muted">
                                        Created: {{ search.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                                        {% if search.email_sent %}
                                            | Email sent: {{ search.email_sent_at.strftime('%B %d, %Y at %I:%M %p') }}
                                        {% endif %}
                                    </small>
                                </div>
                                
                                <div class="col-md-4 text-end">
                                    <div class="mb-2">
                                        {% if search.email_sent %}
                                            <i class="bi bi-check-circle text-success me-1"></i>
                                            <small class="text-success">Email Sent</small>
                                        {% else %}
                                            <i class="bi bi-clock text-warning me-1"></i>
                                            <small class="text-warning">No Email Sent</small>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('main.job_detail', id=search.id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye me-1"></i>View
                                        </a>
                                        {% if not search.email_sent %}
                                            <button class="btn btn-sm btn-primary" onclick="sendEmail({{ search.id }})">
                                                <i class="bi bi-envelope me-1"></i>Send Email
                                            </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if searches.pages > 1 %}
            <div class="row mt-4">
                <div class="col-12">
                    <nav aria-label="Job searches pagination">
                        <ul class="pagination justify-content-center">
                            {% if searches.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('main.job_searches', page=searches.prev_num, **request.args) }}">
                                        <i class="bi bi-chevron-left"></i> Previous
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in searches.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != searches.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('main.job_searches', page=page_num, **request.args) }}">
                                                {{ page_num }}
                                            </a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if searches.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('main.job_searches', page=searches.next_num, **request.args) }}">
                                        Next <i class="bi bi-chevron-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            </div>
        {% endif %}

    {% else %}
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="bi bi-search display-4 text-muted mb-3"></i>
                        <h4 class="text-muted">No Job Searches Found</h4>
                        {% if request.args.get('status') or request.args.get('search') %}
                            <p class="text-muted mb-4">No searches match your current filters. Try adjusting your search criteria.</p>
                            <a href="{{ url_for('main.job_searches') }}" class="btn btn-outline-primary me-2">
                                <i class="bi bi-x-circle me-2"></i>Clear Filters
                            </a>
                        {% else %}
                            <p class="text-muted mb-4">Start by searching for companies you want to apply to.</p>
                        {% endif %}
                        <a href="{{ url_for('main.search') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>Start Your First Search
                        </a>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<div id="alert-container"></div>
{% endblock %}

{% block scripts %}
<script>
function sendEmail(searchId) {
    // Redirect to job detail page where they can send email
    window.location.href = `/job-searches/${searchId}`;
}
</script>
{% endblock %}
