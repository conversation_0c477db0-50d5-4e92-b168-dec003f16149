{% extends "base.html" %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Dashboard</h1>
                <a href="{{ url_for('main.search') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>New Search
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <div class="card stat-card text-center">
                <div class="card-body">
                    <div class="stat-number">{{ total_searches }}</div>
                    <div class="stat-label">Total Searches</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card text-center">
                <div class="card-body">
                    <div class="stat-number">{{ emails_sent }}</div>
                    <div class="stat-label">Emails Sent</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card text-center">
                <div class="card-body">
                    <div class="stat-number">{{ email_templates|length }}</div>
                    <div class="stat-label">Email Templates</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card text-center">
                <div class="card-body">
                    <div class="stat-number">{{ smtp_configs|length }}</div>
                    <div class="stat-label">SMTP Configs</div>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- Recent Job Searches -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Job Searches</h5>
                    <a href="{{ url_for('main.job_searches') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if recent_searches %}
                        {% for search in recent_searches %}
                            <div class="job-search-card card mb-3 {{ search.status }}">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-8">
                                            <h6 class="card-title mb-1">
                                                <a href="{{ url_for('main.job_detail', id=search.id) }}" class="text-decoration-none">
                                                    {{ search.company_name }}
                                                </a>
                                            </h6>
                                            {% if search.position %}
                                                <p class="card-text mb-1"><strong>Position:</strong> {{ search.position }}</p>
                                            {% endif %}
                                            {% if search.company_domain %}
                                                <p class="card-text mb-1"><strong>Domain:</strong> {{ search.company_domain }}</p>
                                            {% endif %}
                                            {% if search.hr_email %}
                                                <p class="card-text mb-1"><strong>HR Contact:</strong> {{ search.hr_email }}</p>
                                            {% endif %}
                                            <small class="text-muted">{{ search.created_at.strftime('%B %d, %Y at %I:%M %p') }}</small>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <span class="badge bg-{{ search.get_status_color() }} mb-2">{{ search.status.title() }}</span>
                                            {% if search.email_sent %}
                                                <br><small class="text-success"><i class="bi bi-check-circle me-1"></i>Email Sent</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-search display-4 text-muted mb-3"></i>
                            <h5 class="text-muted">No job searches yet</h5>
                            <p class="text-muted">Start by searching for companies you want to apply to</p>
                            <a href="{{ url_for('main.search') }}" class="btn btn-primary">Start Your First Search</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('main.search') }}" class="btn btn-primary">
                            <i class="bi bi-search me-2"></i>Search Company
                        </a>
                        <a href="{{ url_for('main.send_manual_email') }}" class="btn btn-success">
                            <i class="bi bi-envelope-plus me-2"></i>Send Manual Email
                        </a>
                        <a href="{{ url_for('main.templates') }}" class="btn btn-outline-primary">
                            <i class="bi bi-envelope me-2"></i>Manage Templates
                        </a>
                        <a href="{{ url_for('main.smtp_configs') }}" class="btn btn-outline-primary">
                            <i class="bi bi-server me-2"></i>SMTP Settings
                        </a>
                        <a href="{{ url_for('main.job_searches') }}" class="btn btn-outline-primary">
                            <i class="bi bi-list-ul me-2"></i>View All Searches
                        </a>
                    </div>
                </div>
            </div>

            <!-- Configuration Status -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Configuration Status</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Email Templates</span>
                            {% if email_templates %}
                                <span class="badge bg-success">{{ email_templates|length }} configured</span>
                            {% else %}
                                <span class="badge bg-warning">Not configured</span>
                            {% endif %}
                        </div>
                        {% if not email_templates %}
                            <small class="text-muted">Create email templates to send professional messages</small>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>SMTP Configuration</span>
                            {% if smtp_configs %}
                                {% set verified_configs = smtp_configs|selectattr('is_verified')|list %}
                                {% if verified_configs %}
                                    <span class="badge bg-success">{{ verified_configs|length }} verified</span>
                                {% else %}
                                    <span class="badge bg-warning">Not verified</span>
                                {% endif %}
                            {% else %}
                                <span class="badge bg-danger">Not configured</span>
                            {% endif %}
                        </div>
                        {% if not smtp_configs %}
                            <small class="text-muted">Configure SMTP to send emails</small>
                        {% elif not smtp_configs|selectattr('is_verified')|list %}
                            <small class="text-muted">Test your SMTP configurations</small>
                        {% endif %}
                    </div>

                    {% if not email_templates or not smtp_configs %}
                        <div class="alert alert-info">
                            <small>
                                <i class="bi bi-info-circle me-1"></i>
                                Complete your setup to start sending emails
                            </small>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
