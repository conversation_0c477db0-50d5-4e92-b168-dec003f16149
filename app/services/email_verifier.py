import requests
import re
import dns.resolver
import socket
from typing import Dict, List, Optional
from config import Config
import time

class EmailVerifier:
    """
    Email verification service using multiple verification methods
    Validates email addresses before sending cold emails
    """
    
    def __init__(self):
        self.hunter_api_key = Config.HUNTER_API_KEY
        self.clearout_api_key = Config.CLEAROUT_API_KEY
        self.zerobounce_api_key = Config.ZEROBOUNCE_API_KEY
        
        # API endpoints
        self.hunter_verify_url = "https://api.hunter.io/v2/email-verifier"
        self.clearout_verify_url = "https://api.clearout.io/v2/email_verify/instant"
        self.zerobounce_verify_url = "https://api.zerobounce.net/v2/validate"
    
    def verify_email_list(self, email_contacts: List[Dict]) -> List[Dict]:
        """
        Verify a list of email contacts and add verification status
        """
        print(f"\n📧 Email Verification: Verifying {len(email_contacts)} email addresses...")
        
        verified_contacts = []
        
        for i, contact in enumerate(email_contacts):
            email = contact.get('email', '')
            if not email:
                continue
            
            print(f"  {i+1}/{len(email_contacts)} Verifying {email}...")
            
            # Perform verification
            verification_result = self.verify_single_email(email)
            
            # Add verification data to contact
            contact.update({
                'email_verification': verification_result,
                'is_valid': verification_result.get('is_valid', False),
                'verification_score': verification_result.get('score', 0),
                'deliverability': verification_result.get('deliverability', 'unknown'),
                'verification_source': verification_result.get('source', 'basic')
            })
            
            # Adjust confidence based on verification
            if verification_result.get('is_valid'):
                contact['confidence'] = min(100, contact.get('confidence', 50) + 20)
            else:
                contact['confidence'] = max(10, contact.get('confidence', 50) - 30)
            
            verified_contacts.append(contact)
            
            # Rate limiting
            time.sleep(0.5)
        
        # Sort by verification score and confidence
        verified_contacts.sort(key=lambda x: (
            -x.get('verification_score', 0),
            -x.get('confidence', 0),
            x.get('is_valid', False)
        ), reverse=True)
        
        valid_count = sum(1 for c in verified_contacts if c.get('is_valid'))
        print(f"  ✅ Verification complete: {valid_count}/{len(verified_contacts)} emails are valid")
        
        return verified_contacts
    
    def verify_single_email(self, email: str) -> Dict:
        """
        Verify a single email address using multiple methods
        """
        if not email or '@' not in email:
            return {
                'is_valid': False,
                'score': 0,
                'deliverability': 'invalid',
                'source': 'format_check',
                'details': 'Invalid email format'
            }
        
        # Try verification methods in order of preference
        verification_methods = [
            ('Hunter.io', self._verify_with_hunter),
            ('Clearout', self._verify_with_clearout),
            ('ZeroBounce', self._verify_with_zerobounce),
            ('DNS/SMTP Check', self._verify_with_dns_smtp),
            ('Basic Format Check', self._basic_format_check)
        ]
        
        for method_name, method_func in verification_methods:
            try:
                result = method_func(email)
                if result and result.get('score', 0) > 0:
                    result['source'] = method_name
                    return result
            except Exception as e:
                print(f"    ⚠️ {method_name} verification failed: {e}")
                continue
        
        # Fallback result
        return {
            'is_valid': False,
            'score': 0,
            'deliverability': 'unknown',
            'source': 'verification_failed',
            'details': 'All verification methods failed'
        }
    
    def _verify_with_hunter(self, email: str) -> Optional[Dict]:
        """Verify email using Hunter.io API"""
        if not self.hunter_api_key:
            return None
        
        try:
            params = {
                'email': email,
                'api_key': self.hunter_api_key
            }
            
            response = requests.get(self.hunter_verify_url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                verification_data = data.get('data', {})
                
                result = verification_data.get('result', 'unknown')
                score = verification_data.get('score', 0)
                
                return {
                    'is_valid': result in ['deliverable', 'risky'],
                    'score': score,
                    'deliverability': result,
                    'details': f"Hunter.io result: {result}, score: {score}"
                }
            
        except Exception as e:
            print(f"      Hunter.io verification error: {e}")
        
        return None
    
    def _verify_with_clearout(self, email: str) -> Optional[Dict]:
        """Verify email using Clearout API"""
        if not self.clearout_api_key:
            return None
        
        try:
            headers = {
                'Authorization': f'Bearer {self.clearout_api_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'email': email
            }
            
            response = requests.post(
                self.clearout_verify_url, 
                headers=headers, 
                json=data, 
                timeout=10
            )
            
            if response.status_code == 200:
                result_data = response.json()
                status = result_data.get('status', 'unknown')
                
                score_map = {
                    'valid': 90,
                    'invalid': 0,
                    'risky': 50,
                    'unknown': 30
                }
                
                return {
                    'is_valid': status == 'valid',
                    'score': score_map.get(status, 30),
                    'deliverability': status,
                    'details': f"Clearout result: {status}"
                }
            
        except Exception as e:
            print(f"      Clearout verification error: {e}")
        
        return None
    
    def _verify_with_zerobounce(self, email: str) -> Optional[Dict]:
        """Verify email using ZeroBounce API"""
        if not self.zerobounce_api_key:
            return None
        
        try:
            params = {
                'api_key': self.zerobounce_api_key,
                'email': email
            }
            
            response = requests.get(self.zerobounce_verify_url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                status = data.get('status', 'unknown')
                
                score_map = {
                    'valid': 95,
                    'invalid': 0,
                    'catch-all': 60,
                    'unknown': 40,
                    'spamtrap': 0,
                    'abuse': 0,
                    'do_not_mail': 0
                }
                
                return {
                    'is_valid': status == 'valid',
                    'score': score_map.get(status, 40),
                    'deliverability': status,
                    'details': f"ZeroBounce result: {status}"
                }
            
        except Exception as e:
            print(f"      ZeroBounce verification error: {e}")
        
        return None
    
    def _verify_with_dns_smtp(self, email: str) -> Dict:
        """Verify email using DNS and SMTP checks"""
        try:
            domain = email.split('@')[1]
            
            # DNS MX record check
            try:
                mx_records = dns.resolver.resolve(domain, 'MX')
                if not mx_records:
                    return {
                        'is_valid': False,
                        'score': 0,
                        'deliverability': 'no_mx_record',
                        'details': 'No MX record found for domain'
                    }
            except Exception:
                return {
                    'is_valid': False,
                    'score': 0,
                    'deliverability': 'dns_error',
                    'details': 'DNS resolution failed'
                }
            
            # Basic SMTP check (simplified)
            try:
                mx_record = str(mx_records[0].exchange)
                
                # Try to connect to SMTP server
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                    sock.settimeout(5)
                    result = sock.connect_ex((mx_record, 25))
                    
                    if result == 0:
                        return {
                            'is_valid': True,
                            'score': 70,
                            'deliverability': 'smtp_connectable',
                            'details': 'SMTP server is reachable'
                        }
                    else:
                        return {
                            'is_valid': False,
                            'score': 30,
                            'deliverability': 'smtp_unreachable',
                            'details': 'SMTP server unreachable'
                        }
                        
            except Exception:
                return {
                    'is_valid': True,  # Assume valid if we can't check SMTP
                    'score': 50,
                    'deliverability': 'smtp_check_failed',
                    'details': 'SMTP check failed, but domain has MX record'
                }
                
        except Exception as e:
            return {
                'is_valid': False,
                'score': 0,
                'deliverability': 'verification_error',
                'details': f'DNS/SMTP verification error: {e}'
            }
    
    def _basic_format_check(self, email: str) -> Dict:
        """Basic email format validation"""
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        if re.match(email_pattern, email):
            return {
                'is_valid': True,
                'score': 40,
                'deliverability': 'format_valid',
                'details': 'Email format is valid'
            }
        else:
            return {
                'is_valid': False,
                'score': 0,
                'deliverability': 'format_invalid',
                'details': 'Email format is invalid'
            }
    
    def get_verification_summary(self, verified_contacts: List[Dict]) -> Dict:
        """Get summary statistics of email verification results"""
        if not verified_contacts:
            return {}
        
        total = len(verified_contacts)
        valid = sum(1 for c in verified_contacts if c.get('is_valid'))
        high_confidence = sum(1 for c in verified_contacts if c.get('verification_score', 0) >= 80)
        
        deliverability_stats = {}
        for contact in verified_contacts:
            deliverability = contact.get('deliverability', 'unknown')
            deliverability_stats[deliverability] = deliverability_stats.get(deliverability, 0) + 1
        
        return {
            'total_emails': total,
            'valid_emails': valid,
            'invalid_emails': total - valid,
            'validity_rate': round((valid / total) * 100, 2) if total > 0 else 0,
            'high_confidence_emails': high_confidence,
            'deliverability_breakdown': deliverability_stats,
            'recommended_for_outreach': [
                c for c in verified_contacts 
                if c.get('is_valid') and c.get('verification_score', 0) >= 60
            ][:10]  # Top 10 recommended emails
        }
