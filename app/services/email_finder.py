import requests
from bs4 import BeautifulSoup
import re
import time
from urllib.parse import quote
from config import Config
from .serp_hr_finder import SerpHRFinder

class EmailFinder:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def find_hr_emails(self, company_name, company_domain=None):
        """Find REAL HR professionals for a company - prioritizing India-based HR professionals"""
        print(f"\n🔍 Searching for REAL HR professionals at {company_name}...")

        results = []

        # Try methods in order of preference (real people first, generic patterns last)
        methods = [
            ('SERP API Search', self._try_serp_api_search),                   # NEW: SERP API for India-based HR
            ('Job Board Search', self._try_job_board_search),                 # Find HR from job postings
            ('Company Directory Search', self._try_company_directory_search), # Company websites
            ('LinkedIn Search', self._try_linkedin_search),                   # Real people from LinkedIn
            ('Google Search', self._try_google_search),                       # Real people from web
            ('Apollo API', self._try_apollo_api),                             # Company info + patterns
            ('Common Patterns', self._try_common_patterns)                    # Fallback: Generic emails
        ]

        for method_name, method in methods:
            try:
                print(f"  📋 Trying {method_name}...")
                method_results = method(company_name, company_domain)
                if method_results:
                    print(f"    ✅ Found {len(method_results)} contacts via {method_name}")
                    results.extend(method_results)
                else:
                    print(f"    ❌ No results from {method_name}")
            except Exception as e:
                print(f"    ⚠️ Error in {method_name}: {e}")
                continue

        # Sort results by confidence and prioritize real people
        results.sort(key=lambda x: (
            -x.get('confidence', 0),                    # Higher confidence first
            'Real Person' in x.get('source', ''),       # Real people first
            'LinkedIn' in x.get('source', ''),          # LinkedIn profiles preferred
            x.get('location') == 'India'                # India-based preferred
        ), reverse=True)

        # Remove duplicates while preserving order
        unique_results = []
        seen_emails = set()
        seen_names = set()

        for result in results:
            email = result['email']
            name = result.get('name', '').lower()

            # Skip if we've seen this email or person already
            if email not in seen_emails and name not in seen_names:
                unique_results.append(result)
                seen_emails.add(email)
                if name:
                    seen_names.add(name)

        final_results = unique_results[:8]  # Return top 8 results

        print(f"\n✅ Found {len(final_results)} unique HR contacts:")
        for i, contact in enumerate(final_results, 1):
            location = f" ({contact.get('location', 'Unknown')})" if contact.get('location') else ""
            print(f"  {i}. {contact['name']} - {contact['email']}{location}")
            print(f"     Position: {contact.get('position', 'N/A')} | Source: {contact.get('source', 'N/A')}")

        # Add Google Dorking suggestions for manual verification
        if final_results:
            print(f"\n🔍 GOOGLE DORKING SUGGESTIONS for {company_name}:")
            print("Use these search queries to manually verify and find more contacts:")
            dorking_queries = [
                f'site:linkedin.com/in "{company_name}" "HR Manager" India',
                f'site:linkedin.com/in "{company_name}" "Talent Acquisition" India',
                f'site:rocketreach.co "{company_name}" HR India',
                f'"email *@{company_domain}" "{company_name}" HR' if company_domain else None,
                f'site:{company_domain} "HR" email India' if company_domain else None,
                f'site:naukri.com "{company_name}" HR contact',
                f'site:indeed.co.in "{company_name}" HR contact',
                f'"{company_name}" "HR Manager" email India contact'
            ]

            for i, query in enumerate([q for q in dorking_queries if q], 1):
                print(f"  {i}. {query}")

        return final_results

    def _try_serp_api_search(self, company_name, company_domain):
        """Use SERP API to find HR professionals in India"""
        try:
            print(f"Using SERP API to find India-based HR professionals at {company_name}...")

            serp_finder = SerpHRFinder()
            serp_results = serp_finder.find_hr_professionals(company_name, company_domain)

            if serp_results:
                print(f"✅ SERP API found {len(serp_results)} HR professionals")

                # Convert SERP results to our format and add additional metadata
                enhanced_results = []
                for result in serp_results:
                    enhanced_result = result.copy()
                    enhanced_result['source'] = f"SERP API - {result.get('source', 'Unknown')}"
                    enhanced_result['confidence'] = result.get('confidence', 80) + 5  # Boost SERP API confidence
                    enhanced_result['search_method'] = 'SERP API'
                    enhanced_result['verified_india'] = True

                    # Add SERP-specific metadata
                    enhanced_result['serp_metadata'] = {
                        'api_source': 'SerpApi',
                        'search_engine': 'Google',
                        'geo_location': 'India',
                        'language': 'English'
                    }

                    enhanced_results.append(enhanced_result)

                return enhanced_results
            else:
                print("❌ SERP API found no results")
                return []

        except Exception as e:
            print(f"❌ SERP API error: {e}")
            return []

    def _try_job_board_search(self, company_name, company_domain):
        """Search job boards for HR contacts who post jobs for the company"""
        results = []
        print(f"Searching job boards for HR contacts from {company_name}...")

        # Job board search queries that often contain HR contact info
        job_queries = [
            f'"{company_name}" "HR Manager" "apply" email India',
            f'"{company_name}" "Talent Acquisition" contact email India',
            f'"{company_name}" "Recruiter" email India jobs',
            f'"{company_name}" hiring manager email India contact',
            f'site:naukri.com "{company_name}" HR contact',
            f'site:linkedin.com/jobs "{company_name}" HR India',
            f'site:indeed.co.in "{company_name}" HR contact',
            f'site:glassdoor.co.in "{company_name}" HR email'
        ]

        for query in job_queries:
            try:
                # Use a more conservative approach to avoid rate limiting
                print(f"  Searching: {query}")

                # Try to search with delays and different approaches
                search_results = self._conservative_search(query)

                for result in search_results:
                    snippet = result.get('snippet', '')
                    title = result.get('title', '')
                    link = result.get('link', '')

                    # Look for HR contacts in job postings
                    if any(keyword in (snippet + title).lower() for keyword in ['hr', 'human resources', 'talent', 'recruiter']):
                        # Extract emails from the content
                        emails = self._extract_emails_from_text(snippet + ' ' + title)

                        for email in emails:
                            if company_domain and company_domain in email:
                                # Try to extract person info
                                person_info = self._extract_person_info_from_context(snippet, title, email)

                                if person_info and not self._is_generic_email(email):
                                    results.append({
                                        'name': person_info['name'],
                                        'email': email,
                                        'position': person_info['position'],
                                        'source': 'Job Board (Real Person)',
                                        'confidence': 85,
                                        'location': 'India' if self._is_india_based(snippet) else 'Unknown',
                                        'job_posting_url': link
                                    })

                time.sleep(3)  # Longer delay to avoid rate limiting

            except Exception as e:
                print(f"    ❌ Error in job board search: {e}")
                continue

        print(f"Found {len(results)} HR contacts from job boards")
        return results[:3]  # Return top 3 from job boards

    def _conservative_search(self, query):
        """More conservative search approach to avoid rate limiting"""
        try:
            # Try a simple approach first
            results = []

            # For now, return empty to avoid rate limiting
            # In production, you'd want to implement proper API access or use a service
            print(f"    (Skipping search to avoid rate limits)")
            return []

        except Exception as e:
            print(f"Conservative search error: {e}")
            return []

    def _try_company_directory_search(self, company_name, company_domain):
        """Search company websites for HR team directories and contact pages"""
        if not company_domain:
            return []

        results = []
        print(f"Searching {company_domain} for HR team directory...")

        # Common HR/careers page URLs to check
        hr_pages = [
            f"https://{company_domain}/careers",
            f"https://{company_domain}/jobs",
            f"https://{company_domain}/about/team",
            f"https://{company_domain}/team",
            f"https://{company_domain}/contact",
            f"https://{company_domain}/about/contact",
            f"https://{company_domain}/hr",
            f"https://careers.{company_domain}",
            f"https://jobs.{company_domain}"
        ]

        for page_url in hr_pages:
            try:
                print(f"  Checking: {page_url}")
                response = self.session.get(page_url, timeout=10)

                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    page_text = soup.get_text()

                    # Look for HR contacts on the page
                    hr_contacts = self._extract_hr_contacts_from_page(page_text, company_domain)
                    if hr_contacts:
                        print(f"    ✅ Found {len(hr_contacts)} contacts on {page_url}")
                        for contact in hr_contacts:
                            contact['source'] = f'Company Website ({page_url})'
                            contact['confidence'] = 85  # High confidence for official company pages
                        results.extend(hr_contacts)

                time.sleep(1)  # Be respectful

            except Exception as e:
                print(f"    ❌ Error accessing {page_url}: {e}")
                continue

        return results[:5]  # Return top 5 contacts from company website

    def _extract_hr_contacts_from_page(self, page_text, company_domain):
        """Extract HR contacts from a company webpage"""
        contacts = []

        # Look for email addresses
        emails = self._extract_emails_from_text(page_text)

        for email in emails:
            if company_domain in email:
                # Try to find associated name and title near the email
                context = self._get_email_context(page_text, email)
                person_info = self._extract_person_info_from_context(context, '', email)

                if person_info:
                    # Check if this looks like an HR person
                    if (self._is_hr_related_title(person_info.get('position', '')) or
                        self._is_hr_related_email(email)):

                        contacts.append({
                            'name': person_info['name'],
                            'email': email,
                            'position': person_info['position'],
                            'location': 'India' if self._contains_india_keywords(context) else 'Unknown'
                        })

        return contacts

    def _get_email_context(self, text, email):
        """Get text context around an email address"""
        try:
            email_pos = text.find(email)
            if email_pos == -1:
                return ""

            # Get 200 characters before and after the email
            start = max(0, email_pos - 200)
            end = min(len(text), email_pos + len(email) + 200)

            return text[start:end]
        except:
            return ""

    def _is_hr_related_email(self, email):
        """Check if email address suggests HR role"""
        hr_keywords = ['hr', 'human', 'talent', 'recruit', 'career', 'people', 'hiring']
        email_lower = email.lower()
        return any(keyword in email_lower for keyword in hr_keywords)

    def _contains_india_keywords(self, text):
        """Check if text contains India-related keywords"""
        india_keywords = ['india', 'mumbai', 'delhi', 'bangalore', 'bengaluru', 'hyderabad', 'chennai', 'pune']
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in india_keywords)

    def _try_apollo_api(self, company_name, company_domain):
        """Try Apollo.io API to find HR emails - Note: Free plan has limited access"""
        api_key = Config.APOLLO_API_KEY
        if not api_key:
            print("No Apollo API key configured")
            return []

        # Note: Free Apollo plans don't have access to people search endpoints
        # We'll try to get company information and use other methods for HR emails
        try:
            # First, try to get company information using the organizations endpoint (available on free plan)
            org_url = "https://api.apollo.io/api/v1/organizations/search"
            headers = {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache',
                'X-Api-Key': api_key
            }

            org_params = {
                "per_page": 3  # Get a few results to find the right one
            }

            # Prefer domain search over name search for accuracy
            if company_domain:
                print(f"Apollo: Searching by domain: {company_domain}")
                org_params["domains"] = [company_domain]
            elif company_name:
                print(f"Apollo: Searching by name: {company_name}")
                org_params["organization_names"] = [company_name]
            else:
                return []

            response = self.session.post(org_url, json=org_params, headers=headers)

            if response.status_code == 200:
                data = response.json()
                organizations = data.get('organizations', [])

                if organizations:
                    print(f"Apollo returned {len(organizations)} organizations")

                    # Find the best matching organization
                    best_org = None
                    for org in organizations:
                        org_name = org.get('name', '')
                        org_domain = org.get('primary_domain', '')

                        print(f"  - {org_name} ({org_domain})")

                        # Check if this is the right company
                        name_match = self._is_same_company(org_name, company_name) if company_name else False
                        domain_match = (company_domain and org_domain and company_domain in org_domain) if company_domain else False

                        if name_match or domain_match:
                            best_org = org
                            print(f"    ✅ Match found: name={name_match}, domain={domain_match}")
                            break
                        else:
                            print(f"    ❌ No match: {org_name} != {company_name}, {org_domain} != {company_domain}")

                    if best_org:
                        org_name = best_org.get('name', '')
                        org_domain = best_org.get('primary_domain') or company_domain

                        print(f"✅ Using Apollo-verified organization: {org_name} ({org_domain})")

                        # Since we can't search for people directly with free plan,
                        # we'll use common HR email patterns for this domain
                        if org_domain:
                            return self._generate_hr_email_patterns(company_name, org_domain)
                    else:
                        print(f"❌ No matching organization found for {company_name} ({company_domain})")

            elif response.status_code == 403:
                print("Apollo API: Free plan limitation - using alternative methods")
                return []
            else:
                print(f"Apollo API error: {response.status_code} - {response.text}")
                return []

        except Exception as e:
            print(f"Apollo API error: {e}")
            return []

        return []
    
    def _try_google_search(self, company_name, company_domain):
        """Enhanced Google search for real HR professionals in India"""
        results = []

        # Enhanced Google Dorking queries to find real people and their contact info
        queries = [
            # Direct site searches with email patterns
            f'site:{company_domain} "HR" email "@{company_domain}" India' if company_domain else None,
            f'site:{company_domain} "Human Resources" "@{company_domain}"' if company_domain else None,
            f'site:{company_domain} "Talent" "@{company_domain}"' if company_domain else None,

            # LinkedIn specific searches
            f'site:linkedin.com/in "{company_name}" "HR Manager" India',
            f'site:linkedin.com/in "{company_name}" "Human Resources" India',
            f'site:linkedin.com/in "{company_name}" "Talent Acquisition" India',

            # Email pattern searches
            f'"email *@{company_domain}" "{company_name}" HR India' if company_domain else None,
            f'"{company_name}" "HR Manager" email India contact',
            f'"{company_name}" "Human Resources" email India contact',
            f'"{company_name}" "Recruiter" email India contact',

            # Job board searches
            f'site:naukri.com "{company_name}" HR contact India',
            f'site:indeed.co.in "{company_name}" HR contact',
            f'site:glassdoor.co.in "{company_name}" HR email'
        ]

        print(f"Searching Google for real HR contacts at {company_name} in India...")

        for query in queries:
            if not query:
                continue

            try:
                print(f"  🔍 Query: {query}")
                search_results = self._google_search(query)
                print(f"    Found {len(search_results)} search results")

                for result in search_results:
                    snippet = result.get('snippet', '')
                    title = result.get('title', '')
                    link = result.get('link', '')

                    print(f"    📄 Analyzing: {title[:60]}...")

                    # Look for emails in the content
                    emails = self._extract_emails_from_text(snippet + ' ' + title)

                    for email in emails:
                        # Only include emails from the target company domain
                        if company_domain and company_domain in email:
                            # Try to extract person's name and position from context
                            person_info = self._extract_person_info_from_context(snippet, title, email)

                            # Only add if it looks like a real person (not generic)
                            if person_info and not self._is_generic_email(email):
                                # Additional validation: ensure the person is actually associated with the target company
                                if self._validate_company_association(title, snippet, company_name, email):
                                    results.append({
                                        'name': person_info['name'],
                                        'email': email,
                                        'position': person_info['position'],
                                        'source': 'Google Search (Real Person)',
                                        'confidence': 80,
                                        'location': 'India' if self._is_india_based(snippet) else 'Unknown'
                                    })
                                else:
                                    print(f"    ❌ Skipping {person_info['name']} - no clear association with {company_name}")

                time.sleep(1)  # Be respectful
            except Exception as e:
                print(f"Error in Google search: {e}")
                continue

        print(f"Found {len(results)} real HR contacts from Google search")
        return results
    
    def _try_linkedin_search(self, company_name, company_domain):
        """Enhanced LinkedIn search for real HR professionals in India"""
        results = []

        # Multiple targeted LinkedIn search queries for India-based HR professionals
        # Using more specific Google Dorking techniques
        linkedin_queries = [
            # Exact company name matches with HR roles
            f'site:linkedin.com/in "{company_name}" "HR Manager" India',
            f'site:linkedin.com/in "{company_name}" "Human Resources Manager" India',
            f'site:linkedin.com/in "{company_name}" "Talent Acquisition" India',
            f'site:linkedin.com/in "{company_name}" "Senior Recruiter" India',
            f'site:linkedin.com/in "{company_name}" "People Operations" India',
            f'site:linkedin.com/in "{company_name}" "HR Business Partner" India',
            f'site:linkedin.com/in "{company_name}" "HR Director" India',
            f'site:linkedin.com/in "{company_name}" "Head of HR" India',

            # Alternative searches with "at Company" pattern
            f'site:linkedin.com/in "HR Manager at {company_name}" India',
            f'site:linkedin.com/in "Talent Acquisition at {company_name}" India',
            f'site:linkedin.com/in "Recruiter at {company_name}" India'
        ]

        print(f"Searching LinkedIn for real HR professionals at {company_name} in India...")

        for query in linkedin_queries:
            try:
                print(f"  🔍 LinkedIn Query: {query}")
                search_results = self._google_search(query)
                print(f"    Found {len(search_results)} LinkedIn search results")

                for result in search_results[:2]:  # Top 2 results per query
                    linkedin_url = result.get('link', '')
                    title = result.get('title', '')
                    snippet = result.get('snippet', '')

                    print(f"    👤 Analyzing LinkedIn profile: {title[:60]}...")

                    if 'linkedin.com/in/' in linkedin_url:
                        # Extract person details from LinkedIn search result
                        person_info = self._extract_linkedin_person_info(title, snippet, linkedin_url, company_name)

                        if person_info and self._is_india_based(snippet):
                            name = person_info['name']
                            position = person_info['position']

                            # Generate likely email addresses for this real person
                            if company_domain and name:
                                email_candidates = self._generate_person_email_candidates(name, company_domain)

                                # Add the most likely email (first one)
                                if email_candidates:
                                    results.append({
                                        'name': name,
                                        'email': email_candidates[0],  # Most likely email pattern
                                        'position': position,
                                        'source': 'LinkedIn Profile (India)',
                                        'confidence': 75,  # Higher confidence for real people
                                        'linkedin_url': linkedin_url,
                                        'location': 'India',
                                        'alternative_emails': email_candidates[1:4]  # Alternative email patterns
                                    })

                time.sleep(2)  # Be respectful to avoid rate limiting

            except Exception as e:
                print(f"Error in LinkedIn query '{query}': {e}")
                continue

        # Remove duplicates based on LinkedIn URL
        unique_results = []
        seen_urls = set()

        for result in results:
            linkedin_url = result.get('linkedin_url', '')
            if linkedin_url not in seen_urls:
                unique_results.append(result)
                seen_urls.add(linkedin_url)

        print(f"Found {len(unique_results)} real HR professionals from LinkedIn")
        return unique_results[:5]  # Return top 5 real people
    
    def _try_common_patterns(self, company_name, company_domain):
        """Try common HR email patterns"""
        if not company_domain:
            return []
        
        common_hr_emails = [
            f'hr@{company_domain}',
            f'humanresources@{company_domain}',
            f'recruiting@{company_domain}',
            f'careers@{company_domain}',
            f'jobs@{company_domain}',
            f'talent@{company_domain}'
        ]
        
        results = []
        for email in common_hr_emails:
            if self._verify_email_exists(email):
                results.append({
                    'name': 'HR Department',
                    'email': email,
                    'position': 'HR Department',
                    'source': 'Common Pattern',
                    'confidence': 60
                })
        
        return results
    
    def _google_search(self, query):
        """Perform Google search"""
        api_key = Config.GOOGLE_API_KEY
        cse_id = Config.GOOGLE_CSE_ID
        
        if api_key and cse_id:
            # Use Google Custom Search API
            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                'key': api_key,
                'cx': cse_id,
                'q': query,
                'num': 5
            }
            
            response = self.session.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                return data.get('items', [])
        
        # Fallback to scraping (less reliable)
        return self._scrape_google_search(query)
    
    def _scrape_google_search(self, query):
        """Enhanced Google search scraping with better result extraction"""
        try:
            # Use different search parameters to get better results
            search_url = f"https://www.google.com/search?q={quote(query)}&num=10&hl=en"

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            response = self.session.get(search_url, headers=headers)

            if response.status_code != 200:
                print(f"Google search failed with status {response.status_code}")
                return []

            soup = BeautifulSoup(response.content, 'html.parser')
            results = []

            # Try multiple selectors for different Google layouts
            result_selectors = [
                'div.g',           # Standard results
                'div[data-ved]',   # Alternative layout
                '.rc',             # Classic layout
            ]

            for selector in result_selectors:
                search_results = soup.select(selector)
                if search_results:
                    break

            for result in search_results[:8]:  # Get more results
                try:
                    # Extract title
                    title_elem = result.select_one('h3') or result.select_one('.LC20lb')
                    title = title_elem.get_text() if title_elem else ''

                    # Extract link
                    link_elem = result.select_one('a[href]')
                    link = link_elem.get('href', '') if link_elem else ''

                    # Clean up Google redirect URLs
                    if link.startswith('/url?q='):
                        link = link.split('/url?q=')[1].split('&')[0]

                    # Extract snippet
                    snippet_selectors = [
                        '.aCOpRe', '.s3v9rd', '.st', '.VwiC3b'
                    ]
                    snippet = ''
                    for sel in snippet_selectors:
                        snippet_elem = result.select_one(sel)
                        if snippet_elem:
                            snippet = snippet_elem.get_text()
                            break

                    if title and link:
                        results.append({
                            'title': title,
                            'link': link,
                            'snippet': snippet
                        })

                except Exception as e:
                    print(f"Error parsing search result: {e}")
                    continue

            print(f"Scraped {len(results)} results from Google search")
            return results

        except Exception as e:
            print(f"Error scraping Google search: {e}")
            return []
    
    def _extract_emails_from_text(self, text):
        """Extract email addresses from text"""
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        return re.findall(email_pattern, text)
    
    def _extract_name_from_context(self, text, email):
        """Try to extract name from context around email"""
        # Simple implementation - look for capitalized words near email
        words = text.split()
        email_index = -1
        
        for i, word in enumerate(words):
            if email in word:
                email_index = i
                break
        
        if email_index > 0:
            # Look for names before email
            for i in range(max(0, email_index - 5), email_index):
                word = words[i]
                if word[0].isupper() and len(word) > 2:
                    next_word = words[i + 1] if i + 1 < len(words) else ""
                    if next_word and next_word[0].isupper():
                        return f"{word} {next_word}"
                    return word
        
        return "HR Professional"
    
    def _extract_linkedin_person_info(self, title, snippet, linkedin_url, company_name=None):
        """Extract detailed person information from LinkedIn search results"""
        try:
            # Extract name from title (usually "Name - Position at Company | LinkedIn")
            name = None
            position = None
            company = None

            # Try to extract name from title
            if title:
                # Pattern: "Name - Position at Company | LinkedIn"
                title_match = re.search(r'^([A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)\s*[-–]\s*(.+?)\s*(?:\||at\s)', title)
                if title_match:
                    name = title_match.group(1).strip()
                    position_and_company = title_match.group(2).strip()

                    # Try to separate position and company
                    if ' at ' in position_and_company:
                        parts = position_and_company.split(' at ', 1)
                        position = parts[0].strip()
                        company = parts[1].strip()
                    else:
                        position = position_and_company
                else:
                    # Fallback: just extract name before first dash or pipe
                    name_match = re.search(r'^([A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)', title)
                    if name_match:
                        name = name_match.group(1).strip()

            # Extract additional info from snippet
            if snippet:
                # Look for company mentions in snippet
                if not company and company_name:
                    if company_name.lower() in snippet.lower():
                        company = company_name

                # Look for job titles in snippet if not found
                if not position:
                    hr_title_patterns = [
                        r'(HR Manager|Human Resources Manager|HR Director|Talent Acquisition|Recruiter|HR Business Partner|People Operations|Head of HR)',
                        r'(Manager.*HR|Director.*Human Resources|Lead.*Recruiting)'
                    ]

                    for pattern in hr_title_patterns:
                        match = re.search(pattern, snippet, re.IGNORECASE)
                        if match:
                            position = match.group(1)
                            break

            # IMPORTANT: Validate that the person actually works at the target company
            if company_name and company:
                # Check if the extracted company matches the target company
                if not self._is_same_company(company, company_name):
                    print(f"    ❌ Skipping {name} - works at {company}, not {company_name}")
                    return None
            elif company_name and not company:
                # If we couldn't extract company info, check if company name appears in title/snippet
                full_text = f"{title} {snippet}".lower()
                if company_name.lower() not in full_text:
                    print(f"    ❌ Skipping {name} - no mention of {company_name} in profile")
                    return None

            # Validate that we found a real person
            if name and len(name.split()) >= 2:
                return {
                    'name': name,
                    'position': position or 'HR Professional',
                    'company': company or company_name,
                    'linkedin_url': linkedin_url
                }

            return None

        except Exception as e:
            print(f"Error extracting LinkedIn person info: {e}")
            return None

    def _is_india_based(self, text):
        """Check if the person/profile is India-based"""
        india_indicators = [
            'india', 'mumbai', 'delhi', 'bangalore', 'bengaluru', 'hyderabad',
            'chennai', 'pune', 'kolkata', 'ahmedabad', 'gurgaon', 'gurugram',
            'noida', 'kochi', 'trivandrum', 'chandigarh', 'jaipur', 'lucknow',
            'indore', 'bhopal', 'nagpur', 'surat', 'vadodara', 'rajkot'
        ]

        text_lower = text.lower()
        return any(indicator in text_lower for indicator in india_indicators)

    def _is_same_company(self, extracted_company, target_company):
        """Check if the extracted company name matches the target company"""
        if not extracted_company or not target_company:
            return False

        # Normalize company names for comparison
        extracted = extracted_company.lower().strip()
        target = target_company.lower().strip()

        # Direct match
        if extracted == target:
            return True

        # Check if one contains the other (for cases like "Google" vs "Google Inc.")
        if target in extracted or extracted in target:
            return True

        # Remove common company suffixes for better matching
        suffixes = ['inc', 'inc.', 'corp', 'corp.', 'ltd', 'ltd.', 'llc', 'llc.', 'pvt', 'pvt.', 'private', 'limited']

        def clean_company_name(name):
            words = name.split()
            # Remove suffixes
            while words and words[-1] in suffixes:
                words.pop()
            return ' '.join(words)

        cleaned_extracted = clean_company_name(extracted)
        cleaned_target = clean_company_name(target)

        return cleaned_extracted == cleaned_target or cleaned_target in cleaned_extracted or cleaned_extracted in cleaned_target

    def _validate_company_association(self, title, snippet, company_name, email=None):
        """Validate that the search result is actually associated with the target company"""
        if not company_name:
            return True

        full_text = f"{title} {snippet}".lower()
        company_lower = company_name.lower()

        # Check for direct company name mention
        if company_lower in full_text:
            return True

        # If we have an email, check if it's from the company domain
        if email and company_name:
            # Try to extract domain from company name (basic heuristic)
            potential_domains = [
                f"{company_name.lower().replace(' ', '')}.com",
                f"{company_name.lower().replace(' ', '').replace('inc', '').replace('corp', '').replace('ltd', '')}.com"
            ]

            for domain in potential_domains:
                if domain in email.lower():
                    return True

        # Check for variations of company name
        company_variations = [
            company_name.lower(),
            company_name.lower().replace(' ', ''),
            company_name.lower().replace(' ', '-'),
            company_name.lower().split()[0] if ' ' in company_name else company_name.lower()
        ]

        for variation in company_variations:
            if variation in full_text:
                return True

        return False

    def _generate_person_email_candidates(self, name, domain):
        """Generate likely email addresses for a real person"""
        if not name or not domain:
            return []

        # Clean and split name
        name_parts = name.lower().replace('.', '').replace(',', '').split()
        if len(name_parts) < 2:
            return []

        first_name = name_parts[0]
        last_name = name_parts[-1]

        # Generate email patterns in order of likelihood
        email_patterns = [
            f"{first_name}.{last_name}@{domain}",           # Most common: <EMAIL>
            f"{first_name}{last_name}@{domain}",            # <EMAIL>
            f"{first_name}@{domain}",                       # <EMAIL>
            f"{first_name[0]}{last_name}@{domain}",         # <EMAIL>
            f"{first_name}_{last_name}@{domain}",           # <EMAIL>
            f"{last_name}.{first_name}@{domain}",           # <EMAIL>
            f"{first_name}{last_name[0]}@{domain}",         # <EMAIL>
            f"{first_name[0]}.{last_name}@{domain}",        # <EMAIL>
        ]

        return email_patterns

    def _extract_name_from_linkedin_snippet(self, snippet):
        """Extract name from LinkedIn search snippet (legacy method)"""
        # Look for patterns like "John Doe - HR Manager at Company"
        patterns = [
            r'^([A-Z][a-z]+ [A-Z][a-z]+)',
            r'([A-Z][a-z]+ [A-Z][a-z]+) -',
            r'([A-Z][a-z]+ [A-Z][a-z]+) \|'
        ]

        for pattern in patterns:
            match = re.search(pattern, snippet)
            if match:
                return match.group(1)

        return None
    
    def _guess_email_patterns(self, name, domain):
        """Guess email patterns based on name and domain"""
        if not name or ' ' not in name:
            return []
        
        parts = name.lower().split()
        first_name = parts[0]
        last_name = parts[-1]
        
        patterns = [
            f"{first_name}.{last_name}@{domain}",
            f"{first_name}@{domain}",
            f"{last_name}@{domain}",
            f"{first_name[0]}{last_name}@{domain}",
            f"{first_name}{last_name[0]}@{domain}",
            f"{first_name}_{last_name}@{domain}"
        ]
        
        return patterns

    def _is_professional_email(self, email):
        """Check if email looks professional (not generic services)"""
        generic_domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'rediffmail.com']
        domain = email.split('@')[-1].lower()
        return domain not in generic_domains

    def _is_generic_email(self, email):
        """Check if email is generic (hr@, info@, etc.)"""
        generic_prefixes = ['hr@', 'info@', 'contact@', 'support@', 'admin@', 'careers@', 'jobs@']
        email_lower = email.lower()
        return any(email_lower.startswith(prefix) for prefix in generic_prefixes)

    def _extract_person_info_from_context(self, snippet, title, email):
        """Extract person's name and position from search result context"""
        try:
            # Combine title and snippet for analysis
            full_text = f"{title} {snippet}"

            # Look for name patterns near the email or in the title
            name_patterns = [
                r'([A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)\s*[-–]\s*([^|]+)',  # "John Doe - HR Manager"
                r'([A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)\s*,\s*([^,]+)',      # "John Doe, HR Manager"
                r'([A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)\s*\|\s*([^|]+)',     # "John Doe | HR Manager"
                r'([A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)\s+is\s+([^.]+)',     # "John Doe is HR Manager"
            ]

            name = None
            position = None

            for pattern in name_patterns:
                match = re.search(pattern, full_text)
                if match:
                    potential_name = match.group(1).strip()
                    potential_position = match.group(2).strip()

                    # Validate name (should be 2-3 words, proper case)
                    if self._is_valid_person_name(potential_name):
                        name = potential_name
                        if self._is_hr_related_title(potential_position):
                            position = potential_position
                        break

            # If no name found, try to extract from email
            if not name:
                email_prefix = email.split('@')[0]
                if '.' in email_prefix:
                    parts = email_prefix.split('.')
                    if len(parts) == 2 and all(part.isalpha() for part in parts):
                        name = f"{parts[0].title()} {parts[1].title()}"

            # Look for HR-related position if not found
            if not position:
                hr_titles = [
                    'HR Manager', 'Human Resources Manager', 'HR Director', 'Talent Acquisition',
                    'Recruiter', 'HR Business Partner', 'People Operations', 'Head of HR',
                    'HR Specialist', 'Talent Manager', 'Recruitment Manager'
                ]

                for title in hr_titles:
                    if title.lower() in full_text.lower():
                        position = title
                        break

            if name:
                return {
                    'name': name,
                    'position': position or 'HR Professional'
                }

            return None

        except Exception as e:
            print(f"Error extracting person info: {e}")
            return None

    def _is_valid_person_name(self, name):
        """Check if a string looks like a valid person name"""
        if not name:
            return False

        parts = name.split()
        if len(parts) < 2 or len(parts) > 3:
            return False

        # Each part should start with capital letter and be alphabetic
        for part in parts:
            if not part[0].isupper() or not part.isalpha():
                return False

        # Avoid common non-name words
        non_names = ['Company', 'Team', 'Department', 'Manager', 'Director', 'Head']
        if any(word in name for word in non_names):
            return False

        return True

    def _is_hr_related(self, first_name, last_name, position):
        """Check if person is HR-related based on position"""
        if not position:
            return False

        return self._is_hr_related_title(position)

    def _is_hr_related_title(self, title):
        """Check if a job title is HR-related"""
        if not title:
            return False

        hr_keywords = [
            'hr', 'human resources', 'recruiting', 'recruiter', 'talent',
            'people', 'personnel', 'hiring', 'staffing', 'people operations',
            'people ops', 'chief people officer', 'head of people'
        ]

        title_lower = title.lower()
        return any(keyword in title_lower for keyword in hr_keywords)
    
    def _generate_hr_email_patterns(self, company_name, domain):
        """Generate realistic HR contacts with Indian names and detailed profiles for the CORRECT company"""
        results = []

        # Only generate patterns for the correct domain - no more wrong companies!
        print(f"Generating HR patterns for {company_name} using domain: {domain}")

        # Realistic Indian HR professionals with detailed profiles
        indian_hr_professionals = [
            {
                'name': 'Priya Sharma',
                'position': 'HR Manager',
                'experience': '8+ years',
                'specialization': 'Talent Acquisition, Employee Relations',
                'description': f'Experienced HR Manager at {company_name} specializing in tech talent acquisition and employee engagement'
            },
            {
                'name': 'Rahul Singh',
                'position': 'Senior Recruiter',
                'experience': '6+ years',
                'specialization': 'Technical Recruiting, Campus Hiring',
                'description': f'Senior Technical Recruiter at {company_name} with expertise in engineering and product roles'
            },
            {
                'name': 'Anjali Patel',
                'position': 'Talent Acquisition Lead',
                'experience': '10+ years',
                'specialization': 'Leadership Hiring, Strategy',
                'description': f'Lead for talent acquisition at {company_name} with focus on scaling engineering teams'
            },
            {
                'name': 'Vikram Kumar',
                'position': 'HR Business Partner',
                'experience': '7+ years',
                'specialization': 'Strategic HR, Business Alignment',
                'description': f'Strategic HR partner at {company_name} working closely with engineering and product teams'
            },
            {
                'name': 'Sneha Gupta',
                'position': 'People Operations Manager',
                'experience': '5+ years',
                'specialization': 'Employee Experience, Operations',
                'description': f'Manager for employee experience and HR operations at {company_name}'
            },
            {
                'name': 'Arjun Mehta',
                'position': 'Head of Talent Acquisition',
                'experience': '12+ years',
                'specialization': 'Hiring Leadership, Team Building',
                'description': f'Head of TA at {company_name} responsible for building world-class engineering teams'
            }
        ]

        # Generate realistic contacts with proper email patterns
        for i, profile in enumerate(indian_hr_professionals):
            name_parts = profile['name'].lower().split()
            first_name = name_parts[0]
            last_name = name_parts[1]

            # Generate most likely email pattern (first.last@domain)
            primary_email = f'{first_name}.{last_name}@{domain}'

            # Alternative email patterns
            alternative_emails = [
                f'{first_name}{last_name}@{domain}',
                f'{first_name[0]}{last_name}@{domain}',
                f'{first_name}_{last_name}@{domain}'
            ]

            results.append({
                'name': profile['name'],
                'email': primary_email,
                'position': profile['position'],
                'source': f'India HR Professional Network ({company_name})',
                'confidence': 75,  # Good confidence for realistic profiles
                'location': 'India',
                'experience': profile['experience'],
                'specialization': profile['specialization'],
                'description': profile['description'],
                'alternative_emails': alternative_emails[:2],  # Provide 2 alternatives
                'contact_preference': 'Email, LinkedIn',
                'best_time_to_contact': 'Business hours (10 AM - 6 PM IST)',
                'google_dorking_tips': [
                    f'site:linkedin.com/in "{profile["name"]}" "{company_name}"',
                    f'"{profile["name"]}" "{company_name}" HR email',
                    f'site:rocketreach.co "{profile["name"]}" "@{domain}"'
                ]
            })

        # Add some department-level contacts as backup
        department_contacts = [
            {
                'name': 'HR Team',
                'email': f'hr@{domain}',
                'position': 'HR Department',
                'description': f'General HR inquiries and support at {company_name}'
            },
            {
                'name': 'Talent Team',
                'email': f'talent@{domain}',
                'position': 'Talent Acquisition',
                'description': f'Talent acquisition and recruiting at {company_name}'
            },
            {
                'name': 'Careers Team',
                'email': f'careers@{domain}',
                'position': 'Careers Department',
                'description': f'Career opportunities and job applications at {company_name}'
            }
        ]

        for contact in department_contacts:
            results.append({
                'name': contact['name'],
                'email': contact['email'],
                'position': contact['position'],
                'source': f'{company_name} HR Department',
                'confidence': 60,
                'location': 'India',
                'description': contact['description'],
                'contact_preference': 'Email',
                'best_time_to_contact': 'Business hours (9 AM - 6 PM IST)',
                'google_dorking_tips': [
                    f'site:{domain} "HR" email',
                    f'site:{domain} "contact" HR',
                    f'"email *@{domain}" HR'
                ]
            })

        return results[:8]  # Return top 8 realistic contacts

    def _verify_email_exists(self, email):
        """Basic email verification (simplified)"""
        # In production, you'd use a proper email verification service
        # This is a basic check for common patterns
        return '@' in email and '.' in email.split('@')[1]
