import os
from dotenv import load_dotenv

basedir = os.path.abspath(os.path.dirname(__file__))
load_dotenv(os.path.join(basedir, '.env'))

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(basedir, 'jobsearch.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Email configuration
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # API Keys
    APOLLO_API_KEY = os.environ.get('APOLLO_API_KEY')
    CLEARBIT_API_KEY = os.environ.get('CLEARBIT_API_KEY')
    GOOGLE_API_KEY = os.environ.get('GOOGLE_API_KEY')
    GOOGLE_CSE_ID = os.environ.get('GOOGLE_CSE_ID')
    SERP_API_KEY = os.environ.get('SERP_API_KEY') or 'c1824007c8245f45a13212bad16a4d86581f35eb63b6842cb7eaee14e44bd8d2'

    # Additional Search APIs for fallback
    BING_SEARCH_API_KEY = os.environ.get('BING_SEARCH_API_KEY')
    RAPIDAPI_KEY = os.environ.get('RAPIDAPI_KEY')  # For multiple search APIs
    HUNTER_API_KEY = os.environ.get('HUNTER_API_KEY')  # Email finder service
    CLEAROUT_API_KEY = os.environ.get('CLEAROUT_API_KEY')  # Email verification
    ZEROBOUNCE_API_KEY = os.environ.get('ZEROBOUNCE_API_KEY')  # Email verification
